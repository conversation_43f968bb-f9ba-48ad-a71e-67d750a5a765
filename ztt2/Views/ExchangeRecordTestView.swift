//
//  ExchangeRecordTestView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 兑换记录测试视图
 * 用于测试抽奖记录和兑换记录的统一显示功能
 */
struct ExchangeRecordTestView: View {
    
    @StateObject private var dataManager = DataManager.shared
    @State private var selectedMember: Member?
    @State private var testResults: [String] = []
    @State private var showMemberDetail = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题
                Text("兑换记录测试")
                    .font(.title)
                    .fontWeight(.bold)
                    .padding(.top)
                
                // 成员选择
                memberSelectionSection
                
                // 测试按钮
                testButtonsSection
                
                // 测试结果
                testResultsSection
                
                Spacer()
            }
            .padding()
            .navigationTitle("兑换记录测试")
            .navigationBarTitleDisplayMode(.inline)
        }
        .sheet(isPresented: $showMemberDetail) {
            if let member = selectedMember {
                MemberDetailView(
                    memberId: member.objectID.uriRepresentation().absoluteString
                )
            }
        }
    }
    
    // MARK: - 成员选择区域
    
    private var memberSelectionSection: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("选择测试成员")
                .font(.headline)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(dataManager.members, id: \.objectID) { member in
                        Button(action: {
                            selectedMember = member
                            addTestResult("✅ 选择成员: \(member.name ?? "未知")")
                        }) {
                            VStack {
                                Text(member.name ?? "未知")
                                    .font(.caption)
                                    .foregroundColor(.white)
                                Text("\(member.currentPoints)分")
                                    .font(.caption2)
                                    .foregroundColor(.white.opacity(0.8))
                            }
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(selectedMember?.objectID == member.objectID ? 
                                          Color.blue : Color.gray)
                            )
                        }
                    }
                }
                .padding(.horizontal)
            }
        }
    }
    
    // MARK: - 测试按钮区域
    
    private var testButtonsSection: some View {
        VStack(spacing: 12) {
            Text("测试操作")
                .font(.headline)
            
            HStack(spacing: 12) {
                // 创建兑换记录
                Button("创建兑换记录") {
                    createRedemptionRecord()
                }
                .buttonStyle(.borderedProminent)
                
                // 创建抽奖记录
                Button("创建抽奖记录") {
                    createLotteryRecord()
                }
                .buttonStyle(.borderedProminent)
            }
            
            HStack(spacing: 12) {
                // 查看成员详情
                Button("查看成员详情") {
                    openMemberDetail()
                }
                .buttonStyle(.bordered)
                
                // 清空结果
                Button("清空结果") {
                    clearResults()
                }
                .buttonStyle(.bordered)
            }
        }
    }
    
    // MARK: - 测试结果区域
    
    private var testResultsSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("测试结果")
                .font(.headline)
            
            ScrollView {
                LazyVStack(alignment: .leading, spacing: 4) {
                    ForEach(Array(testResults.enumerated()), id: \.offset) { index, result in
                        Text("\(index + 1). \(result)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .frame(maxWidth: .infinity, alignment: .leading)
                    }
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(8)
            }
            .frame(maxHeight: 200)
        }
    }
    
    // MARK: - 测试方法
    
    /**
     * 创建兑换记录
     */
    private func createRedemptionRecord() {
        guard let member = selectedMember else {
            addTestResult("❌ 请先选择成员")
            return
        }
        
        let prizeName = "测试奖品\(Int.random(in: 1...100))"
        let cost: Int32 = 50
        
        dataManager.createRedemptionRecord(
            for: member,
            prizeName: prizeName,
            cost: cost,
            source: "redemption"
        )
        
        addTestResult("✅ 创建兑换记录: \(prizeName) (消耗\(cost)分)")
    }
    
    /**
     * 创建抽奖记录
     */
    private func createLotteryRecord() {
        guard let member = selectedMember else {
            addTestResult("❌ 请先选择成员")
            return
        }
        
        let toolTypes = ["wheel", "blindbox", "scratchcard"]
        let toolType = toolTypes.randomElement() ?? "wheel"
        let prizeName = "抽奖奖品\(Int.random(in: 1...100))"
        let cost: Int32 = 30
        
        dataManager.createLotteryRecord(
            for: member,
            toolType: toolType,
            prizeResult: prizeName,
            cost: cost
        )
        
        let toolDisplayName = toolType == "wheel" ? "大转盘" : 
                             toolType == "blindbox" ? "盲盒" : "刮刮卡"
        addTestResult("✅ 创建抽奖记录: \(toolDisplayName) - \(prizeName) (消耗\(cost)分)")
    }
    
    /**
     * 打开成员详情页
     */
    private func openMemberDetail() {
        guard let member = selectedMember else {
            addTestResult("❌ 请先选择成员")
            return
        }
        
        showMemberDetail = true
        addTestResult("👤 打开成员详情页，请查看兑换记录标签页")
    }
    
    /**
     * 清空测试结果
     */
    private func clearResults() {
        testResults.removeAll()
    }
    
    /**
     * 添加测试结果
     */
    private func addTestResult(_ result: String) {
        testResults.append(result)
    }
}

#Preview {
    ExchangeRecordTestView()
}
